using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Sub.JakeCRM.Domain.Configuration;
using Sub.JakeCRM.Domain.Entities;
using Sub.JakeCRM.Domain.Interfaces;
using System.Text.Json;

namespace Sub.JakeCRM.Application.Services;

/// <summary>
/// Implementation of configuration service
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;
    private readonly ApplicationOptions _applicationOptions;
    private readonly ServiceBusOptions _serviceBusOptions;
    private readonly ApiClientOptions _apiClientOptions;
    private readonly ProcessingOptions _processingOptions;
    private readonly MonitoringOptions _monitoringOptions;
    private readonly SecurityOptions _securityOptions;
    private readonly NLogOptions _nlogOptions;

    public ConfigurationService(
        IConfiguration configuration,
        ILogger<ConfigurationService> logger,
        IOptions<ApplicationOptions> applicationOptions,
        IOptions<ServiceBusOptions> serviceBusOptions,
        IOptions<ApiClientOptions> apiClientOptions,
        IOptions<ProcessingOptions> processingOptions,
        IOptions<MonitoringOptions> monitoringOptions,
        IOptions<SecurityOptions> securityOptions,
        IOptions<NLogOptions> nlogOptions)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _applicationOptions = applicationOptions?.Value ?? throw new ArgumentNullException(nameof(applicationOptions));
        _serviceBusOptions = serviceBusOptions?.Value ?? throw new ArgumentNullException(nameof(serviceBusOptions));
        _apiClientOptions = apiClientOptions?.Value ?? throw new ArgumentNullException(nameof(apiClientOptions));
        _processingOptions = processingOptions?.Value ?? throw new ArgumentNullException(nameof(processingOptions));
        _monitoringOptions = monitoringOptions?.Value ?? throw new ArgumentNullException(nameof(monitoringOptions));
        _securityOptions = securityOptions?.Value ?? throw new ArgumentNullException(nameof(securityOptions));
        _nlogOptions = nlogOptions?.Value ?? throw new ArgumentNullException(nameof(nlogOptions));

        LogConfigurationSummary();
    }

    public T GetValue<T>(string key, T defaultValue = default!)
    {
        try
        {
            return _configuration.GetValue<T>(key, defaultValue);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get configuration value for key: {Key}. Using default value: {DefaultValue}", key, defaultValue);
            return defaultValue;
        }
    }

    public IConfigurationSection GetSection(string sectionName)
    {
        return _configuration.GetSection(sectionName);
    }

    public T GetOptions<T>() where T : class, new()
    {
        var options = new T();
        var sectionName = GetSectionNameForType<T>();
        
        if (!string.IsNullOrEmpty(sectionName))
        {
            _configuration.GetSection(sectionName).Bind(options);
        }

        return options;
    }

    public bool HasKey(string key)
    {
        return _configuration[key] != null;
    }

    public Dictionary<string, string> GetAllSettings()
    {
        var settings = new Dictionary<string, string>();
        
        foreach (var kvp in _configuration.AsEnumerable())
        {
            if (kvp.Value != null)
            {
                // Mask sensitive values
                var maskedValue = MaskSensitiveValue(kvp.Key, kvp.Value);
                settings[kvp.Key] = maskedValue;
            }
        }

        return settings;
    }

    public ConfigurationValidationResult ValidateConfiguration()
    {
        var result = new ConfigurationValidationResult();

        try
        {
            // Validate Service Bus configuration
            ValidateServiceBusConfiguration(result);

            // Validate API Client configuration
            ValidateApiClientConfiguration(result);

            // Validate Processing configuration
            ValidateProcessingConfiguration(result);

            // Validate Monitoring configuration
            ValidateMonitoringConfiguration(result);

            // Validate Security configuration
            ValidateSecurityConfiguration(result);

            result.IsValid = result.Errors.Count == 0;
            
            if (result.IsValid)
            {
                _logger.LogInformation("Configuration validation passed successfully");
            }
            else
            {
                _logger.LogError("Configuration validation failed with {ErrorCount} errors", result.Errors.Count);
                foreach (var error in result.Errors)
                {
                    _logger.LogError("Configuration error: {Error}", error);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during configuration validation");
            result.Errors.Add($"Validation error: {ex.Message}");
            result.IsValid = false;
        }

        return result;
    }

    public void ReloadConfiguration()
    {
        try
        {
            if (_configuration is IConfigurationRoot configRoot)
            {
                configRoot.Reload();
                _logger.LogInformation("Configuration reloaded successfully");
                LogConfigurationSummary();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reload configuration");
        }
    }

    private void ValidateServiceBusConfiguration(ConfigurationValidationResult result)
    {
        if (string.IsNullOrWhiteSpace(_serviceBusOptions.ConnectionString))
        {
            result.Errors.Add("ServiceBus.ConnectionString is required");
        }

        if (string.IsNullOrWhiteSpace(_serviceBusOptions.QueueName) && 
            (string.IsNullOrWhiteSpace(_serviceBusOptions.TopicName) || string.IsNullOrWhiteSpace(_serviceBusOptions.SubscriptionName)))
        {
            result.Errors.Add("Either ServiceBus.QueueName or both ServiceBus.TopicName and ServiceBus.SubscriptionName must be specified");
        }

        if (_serviceBusOptions.MaxConcurrentCalls <= 0)
        {
            result.Errors.Add("ServiceBus.MaxConcurrentCalls must be greater than 0");
        }

        if (_serviceBusOptions.MaxDeliveryCount <= 0)
        {
            result.Errors.Add("ServiceBus.MaxDeliveryCount must be greater than 0");
        }
    }

    private void ValidateApiClientConfiguration(ConfigurationValidationResult result)
    {
        if (string.IsNullOrWhiteSpace(_apiClientOptions.BaseUrl))
        {
            result.Errors.Add("ApiClient.BaseUrl is required");
        }
        else if (!Uri.TryCreate(_apiClientOptions.BaseUrl, UriKind.Absolute, out _))
        {
            result.Errors.Add("ApiClient.BaseUrl must be a valid URL");
        }

        if (_apiClientOptions.Timeout <= TimeSpan.Zero)
        {
            result.Errors.Add("ApiClient.Timeout must be greater than zero");
        }

        if (_apiClientOptions.RetryCount < 0)
        {
            result.Errors.Add("ApiClient.RetryCount must be greater than or equal to 0");
        }
    }

    private void ValidateProcessingConfiguration(ConfigurationValidationResult result)
    {
        if (_processingOptions.EnableBatching && _processingOptions.MaxBatchSize <= 0)
        {
            result.Errors.Add("Processing.MaxBatchSize must be greater than 0 when batching is enabled");
        }

        if (_processingOptions.EnableDeduplication && _processingOptions.DeduplicationCacheSize <= 0)
        {
            result.Errors.Add("Processing.DeduplicationCacheSize must be greater than 0 when deduplication is enabled");
        }

        if (_processingOptions.ProcessingTimeoutSeconds <= 0)
        {
            result.Errors.Add("Processing.ProcessingTimeoutSeconds must be greater than 0");
        }
    }

    private void ValidateMonitoringConfiguration(ConfigurationValidationResult result)
    {
        if (_monitoringOptions.EnableApplicationInsights && 
            string.IsNullOrWhiteSpace(_monitoringOptions.ApplicationInsightsKey) &&
            string.IsNullOrWhiteSpace(_monitoringOptions.ApplicationInsightsConnectionString))
        {
            result.Errors.Add("Monitoring.ApplicationInsightsKey or ApplicationInsightsConnectionString is required when Application Insights is enabled");
        }

        if (_monitoringOptions.MetricsIntervalSeconds <= 0)
        {
            result.Errors.Add("Monitoring.MetricsIntervalSeconds must be greater than 0");
        }
    }

    private void ValidateSecurityConfiguration(ConfigurationValidationResult result)
    {
        if (_securityOptions.EnableEncryption && string.IsNullOrWhiteSpace(_securityOptions.EncryptionKey))
        {
            result.Errors.Add("Security.EncryptionKey is required when encryption is enabled");
        }

        if (_securityOptions.EnableKeyVault && _securityOptions.KeyVault != null)
        {
            if (string.IsNullOrWhiteSpace(_securityOptions.KeyVault.Url))
            {
                result.Errors.Add("Security.KeyVault.Url is required when Key Vault is enabled");
            }
        }
    }

    private string GetSectionNameForType<T>()
    {
        return typeof(T).Name switch
        {
            nameof(ApplicationOptions) => ApplicationOptions.SectionName,
            nameof(ServiceBusOptions) => ServiceBusOptions.SectionName,
            nameof(ApiClientOptions) => ApiClientOptions.SectionName,
            nameof(ProcessingOptions) => ProcessingOptions.SectionName,
            nameof(MonitoringOptions) => MonitoringOptions.SectionName,
            nameof(SecurityOptions) => SecurityOptions.SectionName,
            nameof(NLogOptions) => NLogOptions.SectionName,
            _ => string.Empty
        };
    }

    private string MaskSensitiveValue(string key, string value)
    {
        var sensitiveKeys = new[] { "password", "secret", "key", "token", "connectionstring" };
        
        if (sensitiveKeys.Any(k => key.ToLowerInvariant().Contains(k)))
        {
            return value.Length > 4 ? $"{value[..4]}****" : "****";
        }

        return value;
    }

    private void LogConfigurationSummary()
    {
        try
        {
            _logger.LogInformation("Configuration Summary:");
            _logger.LogInformation("Application: {Name} v{Version} ({Environment})", 
                _applicationOptions.Name, _applicationOptions.Version, _applicationOptions.Environment);
            _logger.LogInformation("Service Bus: Queue={Queue}, Topic={Topic}, Subscription={Subscription}", 
                _serviceBusOptions.QueueName, _serviceBusOptions.TopicName, _serviceBusOptions.SubscriptionName);
            _logger.LogInformation("API Client: BaseUrl={BaseUrl}, Timeout={Timeout}s, RetryCount={RetryCount}", 
                _apiClientOptions.BaseUrl, _apiClientOptions.Timeout.TotalSeconds, _apiClientOptions.RetryCount);
            _logger.LogInformation("Processing: Batching={Batching}, Deduplication={Deduplication}, Parallel={Parallel}", 
                _processingOptions.EnableBatching, _processingOptions.EnableDeduplication, _processingOptions.EnableParallelProcessing);
            _logger.LogInformation("Monitoring: AppInsights={AppInsights}, Metrics={Metrics}, HealthChecks={HealthChecks}", 
                _monitoringOptions.EnableApplicationInsights, _monitoringOptions.EnableCustomMetrics, _monitoringOptions.HealthChecks.Enabled);
            _logger.LogInformation("Security: Encryption={Encryption}, KeyVault={KeyVault}, Audit={Audit}", 
                _securityOptions.EnableEncryption, _securityOptions.EnableKeyVault, _securityOptions.EnableAuditLogging);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to log configuration summary");
        }
    }
}
