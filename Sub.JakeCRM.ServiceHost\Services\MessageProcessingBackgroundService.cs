using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Sub.JakeCRM.Domain.Interfaces;

namespace Sub.JakeCRM.ServiceHost.Services;

/// <summary>
/// Background service that processes messages from Service Bus
/// </summary>
public class MessageProcessingBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<MessageProcessingBackgroundService> _logger;

    public MessageProcessingBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<MessageProcessingBackgroundService> logger)
    {
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Message Processing Background Service starting...");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var serviceBusReceiver = scope.ServiceProvider.GetRequiredService<IServiceBusReceiver>();
            var messageProcessor = scope.ServiceProvider.GetRequiredService<IMessageProcessor>();

            // Start receiving messages
            await serviceBusReceiver.StartReceivingAsync(
                async (message, cancellationToken) =>
                {
                    return await messageProcessor.ProcessMessageAsync(message, cancellationToken);
                },
                stoppingToken);

            _logger.LogInformation("Message Processing Background Service started successfully");

            // Keep the service running until cancellation is requested
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Message Processing Background Service is stopping due to cancellation");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in Message Processing Background Service");
            throw;
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Message Processing Background Service stopping...");

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var serviceBusReceiver = scope.ServiceProvider.GetRequiredService<IServiceBusReceiver>();
            await serviceBusReceiver.StopReceivingAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Service Bus receiver");
        }

        await base.StopAsync(cancellationToken);
        _logger.LogInformation("Message Processing Background Service stopped");
    }
}
