namespace Sub.JakeCRM.Domain.Configuration;

/// <summary>
/// NLog configuration options
/// </summary>
public class NLogOptions
{
    public const string SectionName = "NLog";

    /// <summary>
    /// Enable NLog auto-reload when config file changes
    /// </summary>
    public bool AutoReload { get; set; } = true;

    /// <summary>
    /// NLog internal log level (Off, Fatal, Error, Warn, Info, Debug, Trace)
    /// </summary>
    public string InternalLogLevel { get; set; } = "Info";

    /// <summary>
    /// Enable NLog internal logging to file
    /// </summary>
    public bool EnableInternalLogging { get; set; } = true;

    /// <summary>
    /// Internal log file path
    /// </summary>
    public string InternalLogFile { get; set; } = "logs/internal-nlog.txt";

    /// <summary>
    /// Log directory path
    /// </summary>
    public string LogDirectory { get; set; } = "logs";

    /// <summary>
    /// Maximum number of archive files to keep
    /// </summary>
    public int MaxArchiveFiles { get; set; } = 30;

    /// <summary>
    /// Archive files every (Day, Hour, Minute)
    /// </summary>
    public string ArchiveEvery { get; set; } = "Day";

    /// <summary>
    /// Enable console logging
    /// </summary>
    public bool EnableConsoleLogging { get; set; } = true;

    /// <summary>
    /// Enable file logging
    /// </summary>
    public bool EnableFileLogging { get; set; } = true;

    /// <summary>
    /// Enable Windows Event Log logging
    /// </summary>
    public bool EnableEventLogLogging { get; set; } = true;

    /// <summary>
    /// Enable structured JSON logging
    /// </summary>
    public bool EnableJsonLogging { get; set; } = true;

    /// <summary>
    /// Enable database logging
    /// </summary>
    public bool EnableDatabaseLogging { get; set; } = false;

    /// <summary>
    /// Database connection string for logging
    /// </summary>
    public string? DatabaseConnectionString { get; set; }

    /// <summary>
    /// Enable performance counter logging
    /// </summary>
    public bool EnablePerformanceCounters { get; set; } = false;

    /// <summary>
    /// Minimum log level for console output
    /// </summary>
    public string ConsoleMinLevel { get; set; } = "Debug";

    /// <summary>
    /// Minimum log level for file output
    /// </summary>
    public string FileMinLevel { get; set; } = "Info";

    /// <summary>
    /// Minimum log level for event log output
    /// </summary>
    public string EventLogMinLevel { get; set; } = "Error";

    /// <summary>
    /// Minimum log level for JSON output
    /// </summary>
    public string JsonMinLevel { get; set; } = "Info";

    /// <summary>
    /// Enable concurrent writes to log files
    /// </summary>
    public bool EnableConcurrentWrites { get; set; } = true;

    /// <summary>
    /// Keep log files open for better performance
    /// </summary>
    public bool KeepFileOpen { get; set; } = false;

    /// <summary>
    /// Custom log layout pattern
    /// </summary>
    public string? CustomLayout { get; set; }

    /// <summary>
    /// Enable log file compression for archives
    /// </summary>
    public bool EnableCompression { get; set; } = false;

    /// <summary>
    /// Log file encoding (UTF-8, ASCII, etc.)
    /// </summary>
    public string FileEncoding { get; set; } = "UTF-8";

    /// <summary>
    /// Buffer size for file logging
    /// </summary>
    public int FileBufferSize { get; set; } = 32768;

    /// <summary>
    /// Flush timeout for file logging in milliseconds
    /// </summary>
    public int FlushTimeoutMs { get; set; } = 1000;

    /// <summary>
    /// Enable async logging for better performance
    /// </summary>
    public bool EnableAsyncLogging { get; set; } = false;

    /// <summary>
    /// Async queue limit
    /// </summary>
    public int AsyncQueueLimit { get; set; } = 10000;

    /// <summary>
    /// Async overflow action (Block, Discard, Grow)
    /// </summary>
    public string AsyncOverflowAction { get; set; } = "Block";

    /// <summary>
    /// Custom targets configuration
    /// </summary>
    public List<CustomTarget> CustomTargets { get; set; } = new();

    /// <summary>
    /// Custom logger rules
    /// </summary>
    public List<LoggerRule> LoggerRules { get; set; } = new();
}

/// <summary>
/// Custom NLog target configuration
/// </summary>
public class CustomTarget
{
    /// <summary>
    /// Target name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Target type (File, Console, Database, etc.)
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Target configuration properties
    /// </summary>
    public Dictionary<string, string> Properties { get; set; } = new();

    /// <summary>
    /// Whether this target is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;
}

/// <summary>
/// Custom logger rule configuration
/// </summary>
public class LoggerRule
{
    /// <summary>
    /// Logger name pattern
    /// </summary>
    public string LoggerNamePattern { get; set; } = "*";

    /// <summary>
    /// Minimum log level
    /// </summary>
    public string MinLevel { get; set; } = "Info";

    /// <summary>
    /// Maximum log level
    /// </summary>
    public string? MaxLevel { get; set; }

    /// <summary>
    /// Target names to write to
    /// </summary>
    public List<string> WriteTo { get; set; } = new();

    /// <summary>
    /// Whether this rule is final (stops processing further rules)
    /// </summary>
    public bool Final { get; set; } = false;

    /// <summary>
    /// Whether this rule is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;
}
