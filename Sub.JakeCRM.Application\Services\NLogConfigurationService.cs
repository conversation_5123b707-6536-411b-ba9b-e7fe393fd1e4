using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using NLog;
using NLog.Config;
using NLog.Targets;
using NLog.Targets.Wrappers;
using Sub.JakeCRM.Infrastructure.Configuration;
using Sub.JakeCRM.Domain.Interfaces;
using System.Text;

namespace Sub.JakeCRM.Application.Services;

/// <summary>
/// Implementation of NLog configuration service
/// </summary>
public class NLogConfigurationService : INLogConfigurationService
{
    private readonly NLogOptions _nlogOptions;
    private readonly ApplicationOptions _applicationOptions;
    private readonly IHostEnvironment _environment;
    private readonly Logger _logger;

    public NLogConfigurationService(
        IOptions<NLogOptions> nlogOptions,
        IOptions<ApplicationOptions> applicationOptions,
        IHostEnvironment environment)
    {
        _nlogOptions = nlogOptions?.Value ?? throw new ArgumentNullException(nameof(nlogOptions));
        _applicationOptions = applicationOptions?.Value ?? throw new ArgumentNullException(nameof(applicationOptions));
        _environment = environment ?? throw new ArgumentNullException(nameof(environment));
        _logger = LogManager.GetCurrentClassLogger();
    }

    public void ConfigureNLog()
    {
        try
        {
            _logger.Info("Configuring NLog with application settings...");

            var config = new LoggingConfiguration();

            // Configure variables
            ConfigureVariables(config);

            // Configure targets
            ConfigureTargets(config);

            // Configure rules
            ConfigureRules(config);

            // Apply configuration
            LogManager.Configuration = config;

            _logger.Info("NLog configuration completed successfully");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to configure NLog");
            throw;
        }
    }

    public void ReconfigureNLog()
    {
        _logger.Info("Reconfiguring NLog...");
        ConfigureNLog();
    }

    public void AddCustomTarget(string name, object target)
    {
        try
        {
            if (target is Target nlogTarget)
            {
                var config = LogManager.Configuration ?? new LoggingConfiguration();
                config.AddTarget(name, nlogTarget);
                LogManager.Configuration = config;
                _logger.Debug("Added custom target: {TargetName}", name);
            }
            else
            {
                _logger.Error("Target must be of type NLog.Targets.Target");
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to add custom target: {TargetName}", name);
        }
    }

    public void AddLoggingRule(string loggerNamePattern, string minLevel, string targetName)
    {
        try
        {
            var config = LogManager.Configuration ?? new LoggingConfiguration();
            var logLevel = GetLogLevel(minLevel);
            var rule = new LoggingRule(loggerNamePattern, logLevel, config.FindTargetByName(targetName));
            config.LoggingRules.Add(rule);
            LogManager.Configuration = config;
            _logger.Debug("Added logging rule: {Pattern} -> {Target}", loggerNamePattern, targetName);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to add logging rule: {Pattern} -> {Target}", loggerNamePattern, targetName);
        }
    }

    private void ConfigureVariables(LoggingConfiguration config)
    {
        config.Variables["logDirectory"] = _nlogOptions.LogDirectory;
        config.Variables["applicationName"] = _applicationOptions.Name;
        config.Variables["environment"] = _environment.EnvironmentName;
        config.Variables["version"] = _applicationOptions.Version;
    }

    private void ConfigureTargets(LoggingConfiguration config)
    {
        // Console target
        if (_nlogOptions.EnableConsoleLogging)
        {
            var consoleTarget = CreateConsoleTarget();
            config.AddTarget("console", consoleTarget);
        }

        // File targets
        if (_nlogOptions.EnableFileLogging)
        {
            var fileTarget = CreateFileTarget("allfile", "all-${shortdate}.log");
            config.AddTarget("allfile", fileTarget);

            var appFileTarget = CreateFileTarget("appfile", "app-${shortdate}.log");
            config.AddTarget("appfile", appFileTarget);

            var errorFileTarget = CreateFileTarget("errorfile", "errors-${shortdate}.log");
            config.AddTarget("errorfile", errorFileTarget);
        }

        // JSON file target
        if (_nlogOptions.EnableJsonLogging)
        {
            var jsonTarget = CreateJsonFileTarget();
            config.AddTarget("jsonfile", jsonTarget);
        }

        // Event Log target (Windows only)
        if (_nlogOptions.EnableEventLogLogging && OperatingSystem.IsWindows())
        {
            var eventLogTarget = CreateEventLogTarget();
            config.AddTarget("eventlog", eventLogTarget);
        }

        // Database target
        if (_nlogOptions.EnableDatabaseLogging && !string.IsNullOrEmpty(_nlogOptions.DatabaseConnectionString))
        {
            var databaseTarget = CreateDatabaseTarget();
            config.AddTarget("database", databaseTarget);
        }

        // Custom targets
        foreach (var customTarget in _nlogOptions.CustomTargets.Where(t => t.Enabled))
        {
            var target = CreateCustomTarget(customTarget);
            if (target != null)
            {
                config.AddTarget(customTarget.Name, target);
            }
        }
    }

    private void ConfigureRules(LoggingConfiguration config)
    {
        // Skip Microsoft logs
        var microsoftRule = new LoggingRule("Microsoft.*", LogLevel.Info, null) { Final = true };
        config.LoggingRules.Add(microsoftRule);

        var httpRule = new LoggingRule("System.Net.Http.*", LogLevel.Info, null) { Final = true };
        config.LoggingRules.Add(httpRule);

        // Application logs to console
        if (_nlogOptions.EnableConsoleLogging && config.FindTargetByName("console") != null)
        {
            var consoleRule = new LoggingRule("Sub.JakeCRM.*", GetLogLevel(_nlogOptions.ConsoleMinLevel), config.FindTargetByName("console"));
            config.LoggingRules.Add(consoleRule);
        }

        // All logs to file
        if (_nlogOptions.EnableFileLogging && config.FindTargetByName("allfile") != null)
        {
            var allFileRule = new LoggingRule("*", GetLogLevel(_nlogOptions.FileMinLevel), config.FindTargetByName("allfile"));
            config.LoggingRules.Add(allFileRule);
        }

        // Application logs to app file
        if (_nlogOptions.EnableFileLogging && config.FindTargetByName("appfile") != null)
        {
            var appFileRule = new LoggingRule("Sub.JakeCRM.*", LogLevel.Debug, config.FindTargetByName("appfile"));
            config.LoggingRules.Add(appFileRule);
        }

        // Error logs to error file
        if (_nlogOptions.EnableFileLogging && config.FindTargetByName("errorfile") != null)
        {
            var errorFileRule = new LoggingRule("*", LogLevel.Error, config.FindTargetByName("errorfile"));
            config.LoggingRules.Add(errorFileRule);
        }

        // JSON logs
        if (_nlogOptions.EnableJsonLogging && config.FindTargetByName("jsonfile") != null)
        {
            var jsonRule = new LoggingRule("Sub.JakeCRM.*", GetLogLevel(_nlogOptions.JsonMinLevel), config.FindTargetByName("jsonfile"));
            config.LoggingRules.Add(jsonRule);
        }

        // Event log
        if (_nlogOptions.EnableEventLogLogging && config.FindTargetByName("eventlog") != null)
        {
            var eventLogRule = new LoggingRule("*", GetLogLevel(_nlogOptions.EventLogMinLevel), config.FindTargetByName("eventlog"));
            config.LoggingRules.Add(eventLogRule);
        }

        // Custom rules
        foreach (var customRule in _nlogOptions.LoggerRules.Where(r => r.Enabled))
        {
            var targets = customRule.WriteTo.Select(t => config.FindTargetByName(t)).Where(t => t != null).ToArray();
            if (targets.Any())
            {
                var rule = new LoggingRule(customRule.LoggerNamePattern, GetLogLevel(customRule.MinLevel), targets.First())
                {
                    Final = customRule.Final
                };

                if (!string.IsNullOrEmpty(customRule.MaxLevel))
                {
                    rule.SetLoggingLevels(GetLogLevel(customRule.MinLevel), GetLogLevel(customRule.MaxLevel));
                }

                config.LoggingRules.Add(rule);
            }
        }
    }

    private Target CreateConsoleTarget()
    {
        var target = new ColoredConsoleTarget("console")
        {
            Layout = "${time:format=HH\\:mm\\:ss.fff} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] ${message} ${exception:format=tostring}"
        };

        // Add color rules
        target.RowHighlightingRules.Add(new ConsoleRowHighlightingRule("level == LogLevel.Debug", ConsoleOutputColor.DarkGray, ConsoleOutputColor.NoChange));
        target.RowHighlightingRules.Add(new ConsoleRowHighlightingRule("level == LogLevel.Info", ConsoleOutputColor.Gray, ConsoleOutputColor.NoChange));
        target.RowHighlightingRules.Add(new ConsoleRowHighlightingRule("level == LogLevel.Warn", ConsoleOutputColor.Yellow, ConsoleOutputColor.NoChange));
        target.RowHighlightingRules.Add(new ConsoleRowHighlightingRule("level == LogLevel.Error", ConsoleOutputColor.Red, ConsoleOutputColor.NoChange));
        target.RowHighlightingRules.Add(new ConsoleRowHighlightingRule("level == LogLevel.Fatal", ConsoleOutputColor.Red, ConsoleOutputColor.White));

        return target;
    }

    private FileTarget CreateFileTarget(string name, string fileName)
    {
        return new FileTarget(name)
        {
            FileName = $"{_nlogOptions.LogDirectory}/{fileName}",
            Layout = "${longdate} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] [${threadid}] ${message} ${exception:format=tostring}",
            ArchiveFileName = $"{_nlogOptions.LogDirectory}/archives/{name}-{{#}}.log",
            ArchiveEvery = GetFileArchivePeriod(_nlogOptions.ArchiveEvery),
            ArchiveNumbering = ArchiveNumberingMode.Rolling,
            MaxArchiveFiles = _nlogOptions.MaxArchiveFiles,
            ConcurrentWrites = _nlogOptions.EnableConcurrentWrites,
            KeepFileOpen = _nlogOptions.KeepFileOpen,
            Encoding = Encoding.GetEncoding(_nlogOptions.FileEncoding),
            BufferSize = _nlogOptions.FileBufferSize,
            AutoFlush = true,
            EnableArchiveFileCompression = _nlogOptions.EnableCompression
        };
    }

    private FileTarget CreateJsonFileTarget()
    {
        return new FileTarget("jsonfile")
        {
            FileName = $"{_nlogOptions.LogDirectory}/structured-${{shortdate}}.json",
            Layout = @"{""timestamp"":""${longdate}"",""level"":""${level:uppercase=true}"",""logger"":""${logger}"",""thread"":""${threadid}"",""message"":""${message}"",""exception"":""${exception:format=tostring}"",""properties"":{""application"":""${var:applicationName}"",""environment"":""${var:environment}"",""version"":""${var:version}"",""machineName"":""${machinename}"",""processId"":""${processid}""}}",
            ArchiveFileName = $"{_nlogOptions.LogDirectory}/archives/structured-{{#}}.json",
            ArchiveEvery = GetFileArchivePeriod(_nlogOptions.ArchiveEvery),
            ArchiveNumbering = ArchiveNumberingMode.Rolling,
            MaxArchiveFiles = _nlogOptions.MaxArchiveFiles,
            ConcurrentWrites = _nlogOptions.EnableConcurrentWrites,
            KeepFileOpen = _nlogOptions.KeepFileOpen
        };
    }

    private Target CreateEventLogTarget()
    {
        // Create a generic target for event log - this would need platform-specific implementation
        var target = new FileTarget("eventlog")
        {
            FileName = $"{_nlogOptions.LogDirectory}/eventlog-${{shortdate}}.log",
            Layout = "${longdate} ${level:uppercase=true} [${logger}] ${message} ${exception:format=tostring}"
        };
        return target;
    }

    private Target? CreateDatabaseTarget()
    {
        if (string.IsNullOrEmpty(_nlogOptions.DatabaseConnectionString))
            return null;

        // For now, create a file target for database logs - this would need proper database target implementation
        var target = new FileTarget("database")
        {
            FileName = $"{_nlogOptions.LogDirectory}/database-${{shortdate}}.log",
            Layout = "${longdate}|${level}|${logger}|${message}|${exception:tostring}|${machinename}|${processid}|${threadid}"
        };

        return target;
    }

    private Target? CreateCustomTarget(CustomTarget customTarget)
    {
        // This is a simplified implementation - in a real scenario, you'd need to handle different target types
        return customTarget.Type.ToLowerInvariant() switch
        {
            "file" => CreateCustomFileTarget(customTarget),
            "console" => CreateConsoleTarget(),
            _ => null
        };
    }

    private FileTarget CreateCustomFileTarget(CustomTarget customTarget)
    {
        var target = new FileTarget(customTarget.Name);
        
        foreach (var property in customTarget.Properties)
        {
            switch (property.Key.ToLowerInvariant())
            {
                case "filename":
                    target.FileName = property.Value;
                    break;
                case "layout":
                    target.Layout = property.Value;
                    break;
                // Add more properties as needed
            }
        }

        return target;
    }

    private static LogLevel GetLogLevel(string level)
    {
        return level.ToLowerInvariant() switch
        {
            "trace" => LogLevel.Trace,
            "debug" => LogLevel.Debug,
            "info" => LogLevel.Info,
            "warn" => LogLevel.Warn,
            "error" => LogLevel.Error,
            "fatal" => LogLevel.Fatal,
            "off" => LogLevel.Off,
            _ => LogLevel.Info
        };
    }

    private static FileArchivePeriod GetFileArchivePeriod(string period)
    {
        return period.ToLowerInvariant() switch
        {
            "minute" => FileArchivePeriod.Minute,
            "hour" => FileArchivePeriod.Hour,
            "day" => FileArchivePeriod.Day,
            "month" => FileArchivePeriod.Month,
            "year" => FileArchivePeriod.Year,
            _ => FileArchivePeriod.Day
        };
    }
}
