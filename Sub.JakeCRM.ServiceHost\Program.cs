﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NLog;
using NLog.Extensions.Logging;
using Sub.JakeCRM.Domain.Configuration;
using Sub.JakeCRM.Application.Services;
using Sub.JakeCRM.Domain.Interfaces;
using Sub.JakeCRM.Infrastructure.Http;
using Sub.JakeCRM.Infrastructure.ServiceBus;
using Sub.JakeCRM.ServiceHost.Services;

namespace Sub.JakeCRM.ServiceHost;

public class Program
{
    public static async Task Main(string[] args)
    {
        // Initialize NLog early
        var logger = LogManager.GetCurrentClassLogger();

        try
        {
            logger.Info("Sub.JakeCRM Service Host initializing...");

            // Test logging functionality
            TestLogging.TestAllLogLevels();

            var host = CreateHostBuilder(args).Build();

            logger.Info("Sub.JakeCRM Service Host starting...");
            await host.RunAsync();
        }
        catch (Exception ex)
        {
            logger.Fatal(ex, "Sub.JakeCRM Service Host terminated unexpectedly");
            Console.WriteLine($"Fatal error: {ex}");
            Environment.Exit(1);
        }
        finally
        {
            // Ensure to flush and stop internal timers/threads before application-exit
            LogManager.Shutdown();
        }
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseWindowsService(options =>
            {
                options.ServiceName = "Sub.JakeCRM.ServiceHost";
            })
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                config.AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json",
                    optional: true, reloadOnChange: true);
                config.AddEnvironmentVariables();
                config.AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // Configuration Options
                services.Configure<ApplicationOptions>(
                    context.Configuration.GetSection(ApplicationOptions.SectionName));
                services.Configure<ServiceBusOptions>(
                    context.Configuration.GetSection(ServiceBusOptions.SectionName));
                services.Configure<ApiClientOptions>(
                    context.Configuration.GetSection(ApiClientOptions.SectionName));
                services.Configure<ProcessingOptions>(
                    context.Configuration.GetSection(ProcessingOptions.SectionName));
                services.Configure<MonitoringOptions>(
                    context.Configuration.GetSection(MonitoringOptions.SectionName));
                services.Configure<SecurityOptions>(
                    context.Configuration.GetSection(SecurityOptions.SectionName));
                services.Configure<NLogOptions>(
                    context.Configuration.GetSection(NLogOptions.SectionName));

                // Configuration Services
                services.AddSingleton<IConfigurationService, ConfigurationService>();
                services.AddSingleton<INLogConfigurationService, NLogConfigurationService>();

                // HTTP Client
                services.AddHttpClient<IApiClient, ApiClient>();

                // Domain Services
                services.AddScoped<IMessageProcessor, MessageProcessorService>();
                services.AddScoped<IServiceBusReceiver, ServiceBusReceiver>();

                // Background Services
                services.AddHostedService<ConfigurationValidationService>();
                services.AddHostedService<MessageProcessingBackgroundService>();

                // Health Checks
                services.AddHealthChecks();
            })
            .ConfigureLogging((context, logging) =>
            {
                logging.ClearProviders();
                logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Trace);
                logging.AddNLog();
            });
}
