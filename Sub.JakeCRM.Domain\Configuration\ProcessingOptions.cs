namespace Sub.JakeCRM.Domain.Configuration;

/// <summary>
/// Message processing configuration options
/// </summary>
public class ProcessingOptions
{
    public const string SectionName = "Processing";

    /// <summary>
    /// Enable message transformation before API submission
    /// </summary>
    public bool EnableMessageTransformation { get; set; } = false;

    /// <summary>
    /// Message transformation rules
    /// </summary>
    public List<TransformationRule> TransformationRules { get; set; } = new();

    /// <summary>
    /// Enable message filtering based on content or properties
    /// </summary>
    public bool EnableMessageFiltering { get; set; } = false;

    /// <summary>
    /// Message filtering rules
    /// </summary>
    public List<FilterRule> FilterRules { get; set; } = new();

    /// <summary>
    /// Enable message batching for API submissions
    /// </summary>
    public bool EnableBatching { get; set; } = false;

    /// <summary>
    /// Maximum batch size for API submissions
    /// </summary>
    public int MaxBatchSize { get; set; } = 10;

    /// <summary>
    /// Batch timeout in seconds (submit partial batch after timeout)
    /// </summary>
    public int BatchTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Enable message deduplication
    /// </summary>
    public bool EnableDeduplication { get; set; } = false;

    /// <summary>
    /// Deduplication cache size (number of message IDs to remember)
    /// </summary>
    public int DeduplicationCacheSize { get; set; } = 10000;

    /// <summary>
    /// Deduplication cache expiry in minutes
    /// </summary>
    public int DeduplicationCacheExpiryMinutes { get; set; } = 60;

    /// <summary>
    /// Enable message archiving after successful processing
    /// </summary>
    public bool EnableArchiving { get; set; } = false;

    /// <summary>
    /// Archive storage connection string (blob storage, file system, etc.)
    /// </summary>
    public string? ArchiveConnectionString { get; set; }

    /// <summary>
    /// Archive container/folder name
    /// </summary>
    public string ArchiveContainer { get; set; } = "processed-messages";

    /// <summary>
    /// Processing timeout per message in seconds
    /// </summary>
    public int ProcessingTimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// Enable parallel processing of messages
    /// </summary>
    public bool EnableParallelProcessing { get; set; } = true;

    /// <summary>
    /// Maximum degree of parallelism
    /// </summary>
    public int MaxDegreeOfParallelism { get; set; } = Environment.ProcessorCount;
}

/// <summary>
/// Message transformation rule
/// </summary>
public class TransformationRule
{
    /// <summary>
    /// Rule name for identification
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Source field path (JSON path or property name)
    /// </summary>
    public string SourcePath { get; set; } = string.Empty;

    /// <summary>
    /// Target field path (JSON path or property name)
    /// </summary>
    public string TargetPath { get; set; } = string.Empty;

    /// <summary>
    /// Transformation type (Copy, Map, Transform, Remove)
    /// </summary>
    public string TransformationType { get; set; } = "Copy";

    /// <summary>
    /// Transformation expression or mapping value
    /// </summary>
    public string? TransformationExpression { get; set; }

    /// <summary>
    /// Whether this rule is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;
}

/// <summary>
/// Message filtering rule
/// </summary>
public class FilterRule
{
    /// <summary>
    /// Rule name for identification
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Field path to evaluate (JSON path or property name)
    /// </summary>
    public string FieldPath { get; set; } = string.Empty;

    /// <summary>
    /// Filter operator (Equals, NotEquals, Contains, StartsWith, EndsWith, Regex)
    /// </summary>
    public string Operator { get; set; } = "Equals";

    /// <summary>
    /// Value to compare against
    /// </summary>
    public string Value { get; set; } = string.Empty;

    /// <summary>
    /// Action to take when rule matches (Include, Exclude, Route)
    /// </summary>
    public string Action { get; set; } = "Include";

    /// <summary>
    /// Target route for routing action
    /// </summary>
    public string? TargetRoute { get; set; }

    /// <summary>
    /// Whether this rule is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;
}
