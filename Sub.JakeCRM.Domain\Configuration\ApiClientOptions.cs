namespace Sub.JakeCRM.Domain.Configuration;

/// <summary>
/// Configuration options for API client
/// </summary>
public class ApiClientOptions
{
    public const string SectionName = "ApiClient";

    /// <summary>
    /// Base URL for the destination API
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Timeout for API requests
    /// </summary>
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Number of retry attempts for failed requests
    /// </summary>
    public int RetryCount { get; set; } = 3;

    /// <summary>
    /// Delay between retry attempts
    /// </summary>
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(2);

    /// <summary>
    /// Default headers to include with all requests
    /// </summary>
    public Dictionary<string, string> DefaultHeaders { get; set; } = new();

    /// <summary>
    /// API key for authentication (if required)
    /// </summary>
    public string? A<PERSON><PERSON><PERSON> { get; set; }

    /// <summary>
    /// API key header name
    /// </summary>
    public string ApiKeyHeaderName { get; set; } = "X-API-Key";

    /// <summary>
    /// Bearer token for authentication (if required)
    /// </summary>
    public string? BearerToken { get; set; }

    /// <summary>
    /// Enable detailed logging of HTTP requests/responses
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// Maximum response content length to log (in bytes)
    /// </summary>
    public int MaxLogContentLength { get; set; } = 4096;
}
