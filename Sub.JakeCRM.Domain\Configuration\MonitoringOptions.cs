namespace Sub.JakeCRM.Domain.Configuration;

/// <summary>
/// Monitoring and observability configuration options
/// </summary>
public class MonitoringOptions
{
    public const string SectionName = "Monitoring";

    /// <summary>
    /// Enable Application Insights telemetry
    /// </summary>
    public bool EnableApplicationInsights { get; set; } = false;

    /// <summary>
    /// Application Insights instrumentation key
    /// </summary>
    public string? ApplicationInsightsKey { get; set; }

    /// <summary>
    /// Application Insights connection string
    /// </summary>
    public string? ApplicationInsightsConnectionString { get; set; }

    /// <summary>
    /// Enable custom metrics collection
    /// </summary>
    public bool EnableCustomMetrics { get; set; } = true;

    /// <summary>
    /// Metrics collection interval in seconds
    /// </summary>
    public int MetricsIntervalSeconds { get; set; } = 60;

    /// <summary>
    /// Enable performance counters
    /// </summary>
    public bool EnablePerformanceCounters { get; set; } = true;

    /// <summary>
    /// Performance counters to collect
    /// </summary>
    public List<string> PerformanceCounters { get; set; } = new()
    {
        "MessagesProcessed",
        "MessagesPerSecond",
        "ProcessingDuration",
        "ErrorRate",
        "ApiResponseTime"
    };

    /// <summary>
    /// Enable distributed tracing
    /// </summary>
    public bool EnableDistributedTracing { get; set; } = false;

    /// <summary>
    /// Tracing service endpoint (Jaeger, Zipkin, etc.)
    /// </summary>
    public string? TracingEndpoint { get; set; }

    /// <summary>
    /// Tracing service name
    /// </summary>
    public string TracingServiceName { get; set; } = "Sub.JakeCRM.ServiceHost";

    /// <summary>
    /// Enable structured logging with correlation IDs
    /// </summary>
    public bool EnableStructuredLogging { get; set; } = true;

    /// <summary>
    /// Log correlation ID header name
    /// </summary>
    public string CorrelationIdHeaderName { get; set; } = "X-Correlation-ID";

    /// <summary>
    /// Enable alerting for critical errors
    /// </summary>
    public bool EnableAlerting { get; set; } = false;

    /// <summary>
    /// Alert webhook URL for notifications
    /// </summary>
    public string? AlertWebhookUrl { get; set; }

    /// <summary>
    /// Alert threshold for error rate (percentage)
    /// </summary>
    public double AlertErrorRateThreshold { get; set; } = 5.0;

    /// <summary>
    /// Alert threshold for processing latency (milliseconds)
    /// </summary>
    public int AlertLatencyThresholdMs { get; set; } = 5000;

    /// <summary>
    /// Health check endpoints configuration
    /// </summary>
    public HealthCheckOptions HealthChecks { get; set; } = new();
}

/// <summary>
/// Health check configuration options
/// </summary>
public class HealthCheckOptions
{
    /// <summary>
    /// Enable health check endpoint
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Health check endpoint path
    /// </summary>
    public string Path { get; set; } = "/health";

    /// <summary>
    /// Detailed health check endpoint path
    /// </summary>
    public string DetailedPath { get; set; } = "/health/detailed";

    /// <summary>
    /// Health check timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Include dependency health checks
    /// </summary>
    public bool IncludeDependencies { get; set; } = true;

    /// <summary>
    /// Dependency health check configurations
    /// </summary>
    public List<DependencyHealthCheck> Dependencies { get; set; } = new();
}

/// <summary>
/// Dependency health check configuration
/// </summary>
public class DependencyHealthCheck
{
    /// <summary>
    /// Dependency name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Dependency type (ServiceBus, Api, Database, etc.)
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Connection string or endpoint for health check
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Health check timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 10;

    /// <summary>
    /// Whether this dependency is critical for application health
    /// </summary>
    public bool IsCritical { get; set; } = true;

    /// <summary>
    /// Custom health check parameters
    /// </summary>
    public Dictionary<string, string> Parameters { get; set; } = new();
}
