namespace Sub.JakeCRM.Domain.Configuration;

/// <summary>
/// Configuration options for Service Bus
/// </summary>
public class ServiceBusOptions
{
    public const string SectionName = "ServiceBus";

    /// <summary>
    /// Service Bus connection string
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Queue name to receive messages from
    /// </summary>
    public string QueueName { get; set; } = string.Empty;

    /// <summary>
    /// Topic name to receive messages from (alternative to queue)
    /// </summary>
    public string? TopicName { get; set; }

    /// <summary>
    /// Subscription name (required when using topic)
    /// </summary>
    public string? SubscriptionName { get; set; }

    /// <summary>
    /// Maximum number of concurrent calls to the message handler
    /// </summary>
    public int MaxConcurrentCalls { get; set; } = 1;

    /// <summary>
    /// Maximum number of messages to prefetch
    /// </summary>
    public int PrefetchCount { get; set; } = 0;

    /// <summary>
    /// Maximum wait time for receiving messages
    /// </summary>
    public TimeSpan MaxWaitTime { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Auto-complete messages after successful processing
    /// </summary>
    public bool AutoCompleteMessages { get; set; } = true;

    /// <summary>
    /// Maximum number of delivery attempts before dead lettering
    /// </summary>
    public int MaxDeliveryCount { get; set; } = 3;

    /// <summary>
    /// Enable session-based message processing
    /// </summary>
    public bool EnableSessions { get; set; } = false;
}
